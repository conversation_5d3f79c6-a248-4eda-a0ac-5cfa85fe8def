import { Link } from '@tanstack/react-router'
import { useAtom, useAtomValue } from 'jotai'
import { ArrowLeftIcon } from 'lucide-react'
import { useMemo } from 'react'

import { SelectIcon } from '@/components/icons/select-icon'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Skeleton } from '@/components/ui/skeleton'

import { fundIdAtom, getFundNamesQueryAtom } from './../atoms'

export function DetailBanner() {
  const id = useAtomValue(fundIdAtom)
  const { data, isLoading } = useAtomValue(getFundNamesQueryAtom)
  const [, setFundIdAtom] = useAtom(fundIdAtom)

  const defaultValue = useMemo(() => {
    return id || (data?.fundNames?.[0]?.value || '')
  }, [id, data])

  return (
    <div className="flex justify-between container pt-14 pb-7">
      <Button variant="ghost" asChild className="flex items-center gap-2">
        <Link to="/">
          <ArrowLeftIcon className="w-6 h-6" />
          <span>返回</span>
        </Link>
      </Button>
      <div>
        {isLoading ? (
          <Skeleton className="h-10 w-24" />
        ) : (
          <Select defaultValue={defaultValue} onValueChange={setFundIdAtom}>
            <SelectTrigger className="w-[280px] h-12 bg-white border border-gray-200 rounded-none pl-12 pr-3 relative">
              <SelectIcon className="w-4 h-4 absolute left-3 top-1/2 -translate-y-1/2" />
              <SelectValue placeholder="基金名称" className="text-gray-800 font-medium" />
              <Select.Icon />
            </SelectTrigger>
            <SelectContent>
              {data?.fundNames.map(({ label, value }) => (
                <SelectItem key={value} value={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>
    </div>
  )
}
