import { PageFooter } from '@/components/page-footer'
import { PageHeader } from '@/components/page-header'

import { HomeBanner } from './components/home-banner'
import { HomeTable } from './components/home-table'

export function HomePage() {
  return (
    <div className="flex min-h-screen flex-col">
      <div className="relative bg-[url('/src/assets/home-banner.jpg')] bg-cover bg-center bg-no-repeat h-[800px]">
        <PageHeader />
        <div className="container">
          <HomeBanner />
        </div>
      </div>
      <div className="p-6">
        {/* <div> */}
        {/*   <HomeForm /> */}
        {/* </div> */}
        <div className="container">
          <HomeTable />
        </div>
      </div>
      <PageFooter />
    </div>
  )
}
