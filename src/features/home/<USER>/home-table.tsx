import type {
  Cell,
  Column,
  ColumnDef,
  Header,
  HeaderGroup,
  Row,
} from '@tanstack/react-table'
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table'
import { useAtomValue } from 'jotai'
import { ArrowDownToLineIcon, ArrowDownUp, ArrowDownWideNarrow, ChevronDown } from 'lucide-react'
import { useMemo, useState } from 'react'

import { ResetIcon } from '@/components/icons/reset-icon'
import { Button } from '@/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import type { POST_API_INPUTS, POST_API_OUTPUTS } from '@/helpers/lib/api-client/types'

import type { FundInfo } from '../atoms'
import { downloadHomeList, getFundListQueryAtom, getFundOptionsQueryAtom } from '../atoms'
import { useDownload } from '../hooks/useDownload'

type SortDirection = 'asc' | 'desc' | false

function SearchButton({
  column,
  list,
}: {
  column: Column<FundInfo, unknown>
  list: string[]
}) {
  const [isOpen, setIsOpen] = useState(false)

  // 获取当前的筛选值（数组形式）
  const currentFilterValue = column.getFilterValue() as string[] | undefined

  const handleValueChange = (value: string) => {
    const currentValues = currentFilterValue || []

    // 单个选项的切换
    if (currentValues.includes(value)) {
      // 如果已选中，则取消选中
      const newValues = currentValues.filter(v => v !== value)
      column.setFilterValue(newValues.length > 0 ? newValues : undefined)
    }
    else {
      // 如果未选中，则添加到选中列表
      column.setFilterValue([...currentValues, value])
    }
  }

  // 判断某个选项是否被选中
  const isSelected = (value: string) => {
    return currentFilterValue?.includes(value) || false
  }

  // 判断是否有选中项
  const hasSelection = currentFilterValue && currentFilterValue.length > 0

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          className="!h-[26px] w-full border-none p-0 hover:bg-transparent focus:ring-0 focus-visible:ring-0 group"
          onClick={() => setIsOpen(!isOpen)}
        >
          <ChevronDown
            className={`h-[13px] w-[26px] transition-all duration-200 group-hover:!text-[rgba(130,247,40,1)] ${isOpen
              ? 'rotate-180'
              : 'rotate-0'
              }`}
            style={{
              color: isOpen ? 'rgba(130, 247, 40, 1)' : hasSelection ? 'rgba(130, 247, 40, 1)' : 'currentColor',
            }}
          />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[593px] p-1 max-h-[662px] overflow-y-auto rounded-none" align="center">
        <div className="flex flex-col">
          {list.map(item => (
            <button
              key={item}
              type="button"
              className={`px-3 py-2 text-none hover:bg-[#01335c] hover:text-white active:bg-[#01335c] active:text-white rounded-none text-left text-[22px] ${isSelected(item) ? 'bg-[#01335c] text-white' : ''
                }`}
              onClick={() => handleValueChange(item)}
            >
              {item}
            </button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  )
}

function SortButton({
  column,
}: {
  column: Column<FundInfo, unknown>
}) {
  const sortDirection = column.getIsSorted() as SortDirection

  const renderIcon = () => {
    if (!sortDirection) {
      return (
        <ArrowDownUp
          className="h-[13px] w-[26px] transition-all duration-200 group-hover:!text-[rgba(130,247,40,1)]"
          style={{
            color: 'currentColor',
          }}
        />
      )
    }
    else if (!(sortDirection === 'asc')) {
      return (
        <ArrowDownWideNarrow
          className="h-[13px] w-[26px] transition-all duration-200 group-hover:!text-[rgba(130,247,40,1)]"
          style={{
            color: 'rgba(130, 247, 40, 1)',
          }}
        />
      )
    }
    return (
      <ArrowDownWideNarrow
        className="h-[13px] w-[26px] transition-all duration-200 group-hover:!text-[rgba(130,247,40,1)] rotate-180"
        style={{
          color: 'rgba(130, 247, 40, 1)',
        }}
      />
    )
  }

  return (
    <Button
      variant="ghost"
      onClick={() => {
        if (!sortDirection) {
          column.toggleSorting(true)
        }
        else if (sortDirection === 'desc') {
          column.toggleSorting(false)
        }
        else {
          column.clearSorting()
        }
      }}
      className="!h-[26px] w-full border-none p-0 hover:bg-transparent focus:ring-0 focus-visible:ring-0 group"
    >
      {renderIcon()}
    </Button>
  )
}

function CustomTableHeader({ title, children }: { title: string, children?: React.ReactNode }) {
  return (
    <div className="flex flex-col items-center justify-center pt-[17px] px-[9px] pb-[15px]">
      <div className="text-[20px] w-[109px] h-[64px] text-wrap text-center items-center flex justify-center">{title}</div>
      <div className="h-[23px]">{children}</div>
    </div>
  )
}

export function HomeTable() {
  const fileName = `${new Date().getFullYear()}-${new Date().getMonth() + 1}-${new Date().getDate()} Finance of Future Fund List.xlsx`

  const { data = [], isLoading } = useAtomValue(getFundListQueryAtom)
  const { data: fundOptions } = useAtomValue(getFundOptionsQueryAtom)
  const { isPending, download } = useDownload<POST_API_INPUTS<'/api/funds/download-home'>, POST_API_OUTPUTS<'/api/funds/download-home'>>(downloadHomeList, fileName)

  async function handleDownload() {
    // 获取当前的筛选条件
    const { columnFilters } = table.getState()

    // 获取当前的排序条件
    const { sorting } = table.getState()

    // 构建查询参数
    const queryParams: POST_API_INPUTS<'/api/funds/download-home'> = {}

    // 处理筛选条件
    columnFilters.forEach((filter) => {
      const { id, value } = filter
      if (value && Array.isArray(value) && value.length > 0) {
        // 将筛选条件映射到查询参数
        switch (id) {
          case 'fundName': {
            queryParams.fundNames = value
            break
          }
          case 'fundCompany': {
            queryParams.companies = value
            break
          }
          case 'fundType': {
            // fundType 在 API 中只接受单个值，取第一个值
            queryParams.fundType = value[0]
            break
          }
        }
      }
    })

    // 处理排序条件
    if (sorting.length > 0) {
      const sortField = sorting[0]
      queryParams.sort = {
        name: sortField.id,
        order: sortField.desc ? 'desc' : 'asc',
      }
    }

    await download(queryParams)
  }

  function handleReset() {
    table.resetColumnFilters()
    table.resetSorting()
  }

  const columns: Array<ColumnDef<FundInfo>> = useMemo(() => {
    const { companies = [], fundNames = [], fundTypes = [] } = fundOptions || {}
    const fundNameList = fundNames.map(({ label }) => label)

    return [
      {
        accessorKey: 'windCode',
        header: () => (
          <CustomTableHeader title="WIND代码" />
        ),

      },
      {
        accessorKey: 'fundName',
        header: ({ column }) => (
          <CustomTableHeader title="基金名称">
            <SearchButton column={column} list={fundNameList} />
          </CustomTableHeader>
        ),
        filterFn: (row, columnId, filterValue) => {
          if (!filterValue || filterValue.length === 0)
            return true
          const cellValue = row.getValue(columnId) as string
          return filterValue.includes(cellValue)
        },
      },
      {
        accessorKey: 'fundCompany',
        header: ({ column }) => (
          <CustomTableHeader title="基金公司">
            <SearchButton column={column} list={companies} />
          </CustomTableHeader>
        ),
        filterFn: (row, columnId, filterValue) => {
          if (!filterValue || filterValue.length === 0)
            return true
          const cellValue = row.getValue(columnId) as string
          return filterValue.includes(cellValue)
        },
      },
      {
        accessorKey: 'fundType',
        header: ({ column }) => (
          <CustomTableHeader title="基金类型">
            <SearchButton column={column} list={fundTypes} />
          </CustomTableHeader>
        ),
        filterFn: (row, columnId, filterValue) => {
          if (!filterValue || filterValue.length === 0)
            return true
          const cellValue = row.getValue(columnId) as string
          return filterValue.includes(cellValue)
        },
      },
      {
        accessorKey: 'issueDate',
        header: ({ column }) => (
          <CustomTableHeader title="发行日期">
            <SortButton column={column} />
          </CustomTableHeader>
        ),
        sortingFn: 'datetime',
      },
      {
        accessorKey: 'highCarbonRatio',
        header: ({ column }) => (
          <CustomTableHeader title="高碳相关投资比例">
            <SortButton column={column} />
          </CustomTableHeader>
        ),
      },
      {
        accessorKey: 'fossilFuelRatio',
        header: ({ column }) => (
          <CustomTableHeader title="化石燃料相关投资比例">
            <SortButton column={column} />
          </CustomTableHeader>
        ),
      },
      {
        accessorKey: 'fundSize',
        header: ({ column }) => (
          <CustomTableHeader title="基金规模（万元）">
            <SortButton column={column} />
          </CustomTableHeader>
        ),
      },
      {
        accessorKey: 'fundSizeUpdateDate',
        header: ({ column }) => (
          <CustomTableHeader title="基金规模更新日期">
            <SortButton column={column} />
          </CustomTableHeader>
        ),
        sortingFn: 'datetime',
      },
    ]
  }, [fundOptions])

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
  })

  return (
    <>
      <div className="rounded-md border">
        <div className="">
          <div className="overflow-auto max-h-[500px]">
            <Table>
              <TableHeader className="bg-[#001e3d] shadow sticky top-0 z-10">
                {table
                  .getHeaderGroups()
                  .map((headerGroup: HeaderGroup<FundInfo>) => (
                    <TableRow key={headerGroup.id}>
                      {headerGroup.headers.map(
                        (header: Header<FundInfo, unknown>) => {
                          return (
                            <TableHead key={header.id} className="bg-[#001e3d] text-white border-b">
                              {header.isPlaceholder
                                ? null
                                : flexRender(
                                    header.column.columnDef.header,
                                    header.getContext(),
                                  )}
                            </TableHead>
                          )
                        },
                      )}
                    </TableRow>
                  ))}
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      Loading...
                    </TableCell>
                  </TableRow>
                ) : table.getRowModel().rows?.length
                  ? (
                      table.getRowModel().rows.map((row: Row<FundInfo>) => (
                        <TableRow
                          key={row.id}
                          data-state={row.getIsSelected() && 'selected'}
                        >
                          {row
                            .getVisibleCells()
                            .map((cell: Cell<FundInfo, unknown>) => (
                              <TableCell key={cell.id} className="text-center w-[124px] p-5 h-[104px] text-wrap whitespace-normal text-[22px]">
                                {flexRender(
                                  cell.column.columnDef.cell,
                                  cell.getContext(),
                                )}
                              </TableCell>
                            ))}
                        </TableRow>
                      ))
                    )
                  : (
                      <TableRow>
                        <TableCell
                          colSpan={columns.length}
                          className="h-24 text-center"
                        >
                          暂无数据
                        </TableCell>
                      </TableRow>
                    )}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
      <div className="mt-6 flex justify-between">
        <Button onClick={handleReset} variant="ghost">
          <ResetIcon className="!size-[38px]" />
          <span className="text-[28px]">重置</span>
        </Button>
        <Button
          onClick={handleDownload}
          disabled={isPending}
          className="bg-[#001e3d] text-white font-normal tracking-normal hover:bg-[#002a4d] disabled:opacity-50 rounded-none"
        >
          <ArrowDownToLineIcon className="w-6 h-6" />
          <span className="">{isPending ? '下载中...' : '下载数据'}</span>
        </Button>
      </div>
    </>
  )
}
